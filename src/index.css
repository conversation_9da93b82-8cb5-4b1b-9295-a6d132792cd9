@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "Virgil";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(./assets/fonts/Virgil.woff2) format("woff2");
  }
  html {
    font-family: "Quicksand", sans-serif;
  }
  :root {
    --radius: 0.5rem;
  }
}

/* Custom styles for Excalidraw toolbar positioning */
@layer components {
  /* Make sure the custom button is properly positioned relative to Excalidraw container */
  .excalidraw-container {
    position: relative;
  }

  /* Ensure our custom button doesn't interfere with Excalidraw's pointer events */
  .custom-excalidraw-button {
    pointer-events: auto;
    z-index: 1000;
  }

  /* Try to shift the hamburger menu to the right - multiple selector attempts */
  .excalidraw .App-menu_top {
    padding-left: 60px !important;
  }

  .excalidraw .App-menu_top .buttonList {
    margin-left: 60px !important;
  }

  .excalidraw .App-menu_top .buttonList button:first-child {
    margin-left: 60px !important;
  }

  /* Alternative selectors for different Excalidraw versions */
  .excalidraw [data-testid="main-menu-trigger"] {
    margin-left: 60px !important;
  }

  .excalidraw .Island button:first-child {
    margin-left: 60px !important;
  }

  /* Ensure proper spacing in the toolbar area */
  .excalidraw .App-top-bar {
    padding-left: 60px !important;
  }
}
