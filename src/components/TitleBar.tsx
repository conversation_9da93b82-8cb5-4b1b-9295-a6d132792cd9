import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type TitleBarProps = {
  title?: string;
  ctaLabel?: string;
  ctaLoadingLabel?: string;
  ctaAction?: () => void;
  ctaClassName?: string;
  isCtaVisible?: boolean;
  isCtaDisabled?: boolean;
  isCtaLoading?: boolean;
  titleExtra?: React.ReactNode;
  extra?: React.ReactNode;
};

export default function TitleBar({
  title,
  ctaLabel,
  ctaLoadingLabel,
  ctaAction,
  ctaClassName,
  isCtaVisible,
  isCtaDisabled,
  isCtaLoading,
  titleExtra,
  extra,
}: TitleBarProps) {
  return (
    <div className="mb-2 grid grid-cols-3 gap-4">
      <div className="col-start-2 flex flex-col items-center justify-center">
        <h1 className="text-center font-virgil text-2xl font-bold" style={{ color: '#E3E3E8' }}>{title}</h1>
        {titleExtra}
      </div>
      <div className="flex justify-end">
        {extra}
        {isCtaVisible && (
          <Button
            variant="ghost"
            className={cn("w-fit font-semibold border-0 shadow-sm transition-colors rounded-lg", ctaClassName)}
            disabled={isCtaDisabled}
            isLoading={isCtaLoading}
            loadingText={ctaLoadingLabel}
            onClick={ctaAction}
            style={{
              backgroundColor: '#23232A',
              borderRadius: '8px',
              color: '#E3E3E8'
            }}
            onMouseEnter={(e) => {
              if (!isCtaDisabled && !isCtaLoading) {
                e.currentTarget.style.backgroundColor = '#363541';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#23232A';
            }}
          >
            {ctaLabel}
          </Button>
        )}
      </div>
    </div>
  );
}
