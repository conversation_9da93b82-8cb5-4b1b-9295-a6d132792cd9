import { usePages, PageData } from "@/hooks/usePages";
import { Link, useLocation } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import Loader from "./Loader";
import { FileText, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import ProfileDropdown from "./ProfileDropdown";

interface SidebarProps {
  className?: string;
}

interface SidebarItemProps {
  page: PageData;
  isActive: boolean;
}

const routes = [
  {
    label: "Home",
    to: "/pages",
  },
  {
    label: "Mermaid",
    to: "/mermaid",
  },
];

function SidebarItem({ page, isActive }: SidebarItemProps) {
  return (
    <Link
      to="/page/$id"
      params={{ id: page.page_id }}
      className={cn(
        "sidebar-item group flex w-full flex-col gap-1 border p-3 text-left transition-all",
        "dark:bg-[#23232A] dark:border-[#404040] dark:hover:bg-[#363541]",
        isActive
          ? "dark:bg-[#363541] dark:border-[#E3E3E8]"
          : "dark:border-[#404040]"
      )}
      style={{
        borderRadius: '8px',
        backgroundColor: isActive ? '#363541' : '#23232A',
        borderColor: isActive ? '#E3E3E8' : '#404040',
        color: '#E3E3E8'
      }}
      onMouseEnter={(e) => {
        if (!isActive) {
          e.currentTarget.style.backgroundColor = '#363541';
        }
      }}
      onMouseLeave={(e) => {
        if (!isActive) {
          e.currentTarget.style.backgroundColor = '#23232A';
        }
      }}
    >
      <div className="flex items-center gap-2">
        <FileText className="h-4 w-4" style={{ color: '#E3E3E8' }} />
        <span
          className="truncate text-sm font-medium"
          style={{ color: '#E3E3E8' }}
        >
          {page.name || "Untitled"}
        </span>
      </div>
      <span className="text-xs" style={{ color: '#E3E3E8', opacity: 0.7 }}>
        {dayjs(page.updated_at).format("MMM DD, YYYY")}
      </span>
    </Link>
  );
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center p-6 text-center">
      <FileText className="h-12 w-12 mb-4" style={{ color: '#E3E3E8', opacity: 0.6 }} />
      <h3 className="text-sm font-medium mb-2" style={{ color: '#E3E3E8' }}>
        No pages yet
      </h3>
      <p className="text-xs mb-4" style={{ color: '#E3E3E8', opacity: 0.7 }}>
        Create your first drawing to get started
      </p>
      <Link to="/pages">
        <Button
          size="sm"
          variant="ghost"
          className="gap-2 border-0 shadow-sm transition-colors rounded-lg"
          style={{
            backgroundColor: '#23232A',
            borderRadius: '8px',
            color: '#E3E3E8'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#363541';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#23232A';
          }}
        >
          <Plus className="h-4 w-4" style={{ color: '#E3E3E8' }} />
          Create Page
        </Button>
      </Link>
    </div>
  );
}

export default function Sidebar({ className }: SidebarProps) {
  const { pages, isLoading } = usePages();
  const location = useLocation();

  // Extract page ID from current location pathname
  const currentPageId = location.pathname.startsWith('/page/')
    ? location.pathname.split('/page/')[1]
    : null;

  // TODO: Add collapsible functionality in the future
  // This sidebar is currently always visible/open by default
  // Future enhancement: Add toggle button and state management for collapse/expand

  return (
    <div
      className={cn(
        "flex h-full w-64 flex-col",
        className
      )}
      style={{ backgroundColor: '#23232A' }}
    >
      {/* Header with Draw Logo */}
      <div
        className="flex items-center justify-between border-b p-4"
        style={{
          borderColor: '#404040',
          backgroundColor: '#23232A'
        }}
      >
        <Link to="/pages" className="flex items-center">
          <h1 className="font-virgil text-2xl font-bold" style={{ color: '#E3E3E8' }}>Draw</h1>
        </Link>
        <Link to="/pages">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 border-0 shadow-sm transition-colors rounded-lg"
            style={{
              backgroundColor: '#23232A',
              borderRadius: '8px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#363541';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#23232A';
            }}
          >
            <Plus className="h-4 w-4" style={{ color: '#E3E3E8' }} />
          </Button>
        </Link>
      </div>

      {/* Pages Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center p-6">
            <Loader />
          </div>
        ) : pages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="h-full overflow-y-auto">
            <div className="space-y-2 p-4">
              {pages.map((page) => (
                <SidebarItem
                  key={page.page_id}
                  page={page}
                  isActive={currentPageId === page.page_id}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="border-t border-gray-200 p-4 dark:border-gray-700">
        <div className="space-y-2 mb-4">
          {routes.map(({ label, to }) => (
            <Link to={to} key={to}>
              {({ isActive }) => (
                <Button
                  className={cn(
                    "flex h-10 w-full items-center justify-center gap-3 text-sm font-light hover:font-bold",
                    isActive ? "font-bold" : "font-medium",
                  )}
                  variant="outline"
                >
                  {label}
                </Button>
              )}
            </Link>
          ))}
        </div>
        <div className="flex justify-center">
          <ProfileDropdown />
        </div>
      </div>
    </div>
  );
}
